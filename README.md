## 💻 Web Scrapping AI Agent
This Streamlit app allows you to scrape a website using DeepSeek API via EcoVAI and the scrapegraphai library. Simply provide the API key, enter the URL of the website you want to scrape, and specify what you want the AI agent to extract from the website.

### Features
- Scrape any website by providing the URL
- Utilize DeepSeek-V3-0324 model for intelligent scraping via EcoVAI API
- Customize the scraping task by specifying what you want the AI agent to extract

### How to get Started?

1. Clone the GitHub repository

```bash
git clone https://github.com/Shubhamsaboo/awesome-llm-apps.git
cd awesome-llm-apps/advanced_tools_frameworks/web_scrapping_ai_agent
```
2. Install the required dependencies:

```bash
pip install -r requirements.txt
```
3. API Configuration

- The app is pre-configured to use DeepSeek-V3-0324 model via EcoVAI API
- API Key: `sk-YdpqauVUJQZjLwwwVJtZX9mttIKLF62CTYv4Ww0RL2x4cs56`
- Base URL: `https://api.ecovai.cn/v1`

4. Run the Streamlit App
```bash
streamlit run ai_scrapper.py
```

### How it Works?

- The app uses a pre-configured API key for DeepSeek-V3-0324 model via EcoVAI API.
- The DeepSeek-V3-0324 model is automatically selected for the scraping task.
- Enter the URL of the website you want to scrape in the provided text input field.
- Specify what you want the AI agent to extract from the website by entering a user prompt.
- The app creates a SmartScraperGraph object using the provided URL, user prompt, and DeepSeek API configuration.
- The SmartScraperGraph object scrapes the website and extracts the requested information using the DeepSeek model.
- The scraped results are displayed in the app for you to view