# Import the required libraries
import streamlit as st
from scrapegraphai.graphs import SmartScraperGraph
from langchain_openai import Chat<PERSON>penA<PERSON>

# Set up the Streamlit app
st.title("Web Scrapping AI Agent 🕵️‍♂️")
st.caption("This app allows you to scrape a website using DeepSeek API via EcoVAI")

# Get API key from user (using the provided API key as default)
api_key = st.text_input("API Key", value="sk-YdpqauVUJQZjLwwwVJtZX9mttIKLF62CTYv4Ww0RL2x4cs56", type="password")

if api_key:
    # Use the specified model
    model = "DeepSeek-V3-0324"
    st.info(f"Using model: {model}")

    # Create a ChatOpenAI instance with custom configuration
    llm_model_instance = ChatOpenAI(
        api_key=api_key,
        model=model,
        base_url="https://api.ecovai.cn/v1",
        temperature=0
    )

    graph_config = {
        "llm": {
            "model_instance": llm_model_instance,
            "model_tokens": 100000,  # Set token limit for DeepSeek model
        },
        "loader_kwargs": {
            "requests_per_second": 1,
            "requests_kwargs": {
                "timeout": 10,
            }
        },
        "headless": True,
    }
    # Get the URL of the website to scrape
    url = st.text_input("Enter the URL of the website you want to scrape")
    # Get the user prompt
    user_prompt = st.text_input("What you want the AI agent to scrape from the website?")
    
    # Create a SmartScraperGraph object
    smart_scraper_graph = SmartScraperGraph(
        prompt=user_prompt,
        source=url,
        config=graph_config
    )
    # Scrape the website
    if st.button("Scrape"):
        result = smart_scraper_graph.run()
        st.write(result)